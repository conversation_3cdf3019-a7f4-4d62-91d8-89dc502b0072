import { NextResponse } from "next/server";
import OpenAI from "openai";
import { zodTextFormat } from "openai/helpers/zod";
import { z } from "zod";
import { pdf2img } from "@/lib/pdf2img";
import { GoogleGenAI, Type } from "@google/genai";
import Anthropic from "@anthropic-ai/sdk";
import fs from "fs/promises";
import path from "path";

const scriptSchema = z.object({
  speaker: z.string(),
  lines: z.string(),
});

const analysisSchema = z.object({
  genre: z.string(),
  scripts: scriptSchema.array(),
});

type Script = z.infer<typeof scriptSchema>;
type Analysis = z.infer<typeof analysisSchema>;

export type { Script, Analysis };

const gemini = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const anthropic = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY });

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const model = formData.get("model") as string;
    const file = formData.get("file") as File;
    const pageNumber = parseInt(formData.get("page") as string) || 0;

    if (!file || file.type !== "application/pdf") {
      return NextResponse.json(
        { error: "Only PDF files are allowed" },
        { status: 400 }
      );
    }

    const pdf = await file.arrayBuffer();
    const pdfImages = await pdf2img(pdf);

    // Validate page number
    if (pageNumber < 0 || pageNumber >= pdfImages.length) {
      return NextResponse.json(
        { error: "Invalid page number" },
        { status: 400 }
      );
    }

    // Use only the specified page
    const targetImage = pdfImages[pageNumber];

    const instruction =
      "優秀な画像認識AIとして、与えられた画像からテキストを抽出し、構造化されたJSONデータに変換しなさい。";
    const prompt = await fs.readFile(
      path.join(process.cwd(), "src/app/api/recognize/prompt.org.md"),
      "utf-8"
    );

    let analysis: Analysis;

    switch (model.split("-")[0]) {
      case "gemini": {
        const response = await gemini.models.generateContent({
          model: model,
          contents: [
            {
              inlineData: {
                mimeType: "image/jpeg" as const,
                data: Buffer.from(targetImage).toString("base64"),
              },
            },
            { text: prompt },
          ],
          config: {
            temperature: 0.2,
            systemInstruction: instruction,
            responseMimeType: "application/json",
            responseSchema: {
              type: Type.OBJECT,
              properties: {
                genre: {
                  type: Type.STRING,
                  description: "Program genre",
                  nullable: false,
                },
                scripts: {
                  type: Type.ARRAY,
                  items: {
                    type: Type.OBJECT,
                    properties: {
                      speaker: {
                        type: Type.STRING,
                        description: "Speaker name",
                        nullable: false,
                      },
                      lines: {
                        type: Type.STRING,
                        description: "Dialogue lines",
                        nullable: false,
                      },
                    },
                    required: ["speaker", "lines"],
                    propertyOrdering: ["speaker", "lines"],
                  },
                },
              },
              required: ["genre", "scripts"],
              propertyOrdering: ["genre", "scripts"],
            },
          },
        });
        analysis = JSON.parse(response.text ?? "{}") as Analysis;
        break;
      }

      case "claude": {
        const response = await anthropic.messages.create({
          model: model,
          max_tokens: 8192,
          temperature: 0.2,
          system: instruction,
          messages: [
            {
              role: "user",
              content: [
                {
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/jpeg" as const,
                    data: Buffer.from(targetImage).toString("base64"),
                  },
                },
                {
                  type: "text",
                  text: prompt,
                },
              ],
            },
          ],
        });
        analysis = JSON.parse(
          response.content[0].type === "text" ? response.content[0].text : "{}"
        ) as Analysis;
        break;
      }

      default: {
        // OpenAI
        const response = await openai.responses.parse({
          model: model,
          temperature: model === "gpt" ? 0.2 : null,
          instructions: instruction,
          input: [
            {
              role: "user",
              content: [
                { type: "input_text", text: prompt },
                {
                  type: "input_image" as const,
                  image_url: `data:image/jpeg;base64,${Buffer.from(
                    targetImage
                  ).toString("base64")}`,
                  detail: "auto" as const,
                },
              ],
            },
          ],
          text: {
            format: zodTextFormat(analysisSchema, "analysis"),
          },
        });
        analysis = response.output_parsed ?? ({} as Analysis);
        break;
      }
    }

    console.log(analysis);
    return NextResponse.json({ analysis });
  } catch (error) {
    console.error("Error processing files:", error);
    return NextResponse.json(
      { error: "Failed to process files" },
      { status: 500 }
    );
  }
}

export const config = {
  api: {
    bodyParser: false,
  },
};

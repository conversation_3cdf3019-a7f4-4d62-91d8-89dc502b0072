"use client";

import PDFUploader from "@/components/PDFUploader";
import { useState } from "react";
import { Analysis } from "@/app/api/recognize/route";
import { MODELS } from "@/config/config";

export default function Home() {
  const [files, setFiles] = useState<FileList | null>(null);
  const [analysis, setAnalysis] = useState<Analysis | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [model, setModel] = useState(MODELS.OpenAI[0].id);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const file = e.target.files[0];
      if (file && file.type === "application/pdf") {
        setFiles(e.target.files);
        setError(null);
      } else {
        setError("PDFファイルを選択してください");
        setFiles(null);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!files) return;

    setIsLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("file", files[0]);
      formData.append("model", model);

      const response = await fetch("/api/recognize", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("ファイルの解析に失敗しました");
      }

      const data = await response.json();
      setAnalysis(data.analysis);
    } catch (err) {
      setError(err instanceof Error ? err.message : "エラーが発生しました");
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <div className="container mx-auto px-4 py-12 lg:px-32 xl:px-64 space-y-12">
      <PDFUploader
        files={files}
        setFiles={setFiles}
        analysis={analysis}
        setAnalysis={setAnalysis}
        isLoading={isLoading}
        setIsLoading={setIsLoading}
        error={error}
        setError={setError}
        model={model}
        setModel={setModel}
        handleSubmit={handleSubmit}
        handleFileChange={handleFileChange}
      />
    </div>
  );
}

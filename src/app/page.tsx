"use client";

import ModelSelector from "@/components/ModelSelector";
import FileUploader from "@/components/FileUploader";
import MessageDisplay from "@/components/MessageDisplay";
import ResultViewer from "@/components/ResultViewer";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { Analysis } from "@/app/api/recognize/route";
import { MODELS } from "@/lib/const";

export default function Home() {
  const [file, setFile] = useState<File | null>(null);
  const [analysis, setAnalysis] = useState<Analysis | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [model, setModel] = useState(MODELS.OpenAI[0].id);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] ?? null;
    if (file && file.type === "application/pdf") {
      setFile(file);
      setError(null);
    } else {
      setError("PDFファイルを選択してください");
      setFile(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) return;

    setIsLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("model", model);

      const response = await fetch("/api/recognize", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("ファイルの解析に失敗しました");
      }

      const data = await response.json();
      setAnalysis(data.analysis);
    } catch (err) {
      setError(err instanceof Error ? err.message : "エラーが発生しました");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12 lg:px-32 xl:px-64 space-y-12">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex flex-col items-center gap-6">
          <ModelSelector model={model} setModel={setModel} />

          <FileUploader file={file} handleFileChange={handleFileChange} />

          <Button
            type="submit"
            disabled={!file || isLoading}
            className="w-full max-w-md"
          >
            {isLoading ? "解析中..." : "ファイルを解析"}
          </Button>
        </div>
      </form>
      <MessageDisplay error={error} />
      <ResultViewer analysis={analysis} />
    </div>
  );
}

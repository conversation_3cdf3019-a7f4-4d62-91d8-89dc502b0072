import { getDocument, GlobalWorkerOptions } from "pdfjs-lib";
import { createCanvas } from "canvas";
import type { PDFDocumentProxy } from "pdfjs-lib/types/src/display/api";
import type { Canvas, CanvasRenderingContext2D } from "canvas";

// Set up the worker source for pdfjs-lib
GlobalWorkerOptions.workerSrc = "pdfjs-lib/build/pdf.worker.js";

/**
 * Converts a PDF ArrayBuffer to an array of JPEG ArrayBuffers.
 *
 * @param pdfBuffer The PDF content as an ArrayBuffer.
 * @returns A promise that resolves to an array of JPEG ArrayBuffers, one for each page.
 */
export async function pdf2img(pdfBuffer: ArrayBuffer): Promise<ArrayBuffer[]> {
  const pdf: PDFDocumentProxy = await getDocument({ data: pdfBuffer }).promise;
  const jpegBuffers: ArrayBuffer[] = [];

  for (let i = 1; i <= pdf.numPages; i++) {
    const page = await pdf.getPage(i);
    const viewport = page.getViewport({ scale: 1.5 });
    const canvas: Canvas = createCanvas(viewport.width, viewport.height);
    const context: CanvasRenderingContext2D = canvas.getContext("2d");

    await page.render({ canvasContext: context, viewport }).promise;

    const jpegBuffer = await new Promise<ArrayBuffer>((resolve, reject) => {
      const chunks: Buffer[] = [];
      const stream = canvas.createJPEGStream({
        quality: 0.9,
        chromaSubsampling: false,
        progressive: false,
      });

      stream.on("data", (chunk) => chunks.push(chunk));
      stream.on("end", () => resolve(Buffer.concat(chunks).buffer));
      stream.on("error", (err) => reject(err));
    });

    jpegBuffers.push(jpegBuffer);
  }

  return jpegBuffers;
}

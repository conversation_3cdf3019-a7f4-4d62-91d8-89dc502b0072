"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Analysis } from "@/app/api/recognize/route";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MODELS } from "@/config/config";

export default function PDFUploader({
  files,
  analysis,
  isLoading,
  error,
  model,
  setModel,
  handleSubmit,
  handleFileChange,
}: {
  files: FileList | null;
  setFiles: React.Dispatch<React.SetStateAction<FileList | null>>;
  analysis: Analysis | null;
  setAnalysis: React.Dispatch<React.SetStateAction<Analysis | null>>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  error: string | null;
  setError: React.Dispatch<React.SetStateAction<string | null>>;
  model: string;
  setModel: React.Dispatch<React.SetStateAction<string>>;
  handleSubmit: (e: React.FormEvent) => void;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}) {
  return (
    <div className="max-w-4xl mx-auto p-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex flex-col items-center gap-6">
          {/* Model Selection */}
          <div className="w-full max-w-md">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              モデルを選択
            </label>
            <Select value={model} onValueChange={setModel}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a model" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(MODELS).map(([provider, modelList]) => (
                  <SelectGroup key={provider}>
                    <SelectLabel>{provider}</SelectLabel>
                    {modelList.map((m) => (
                      <SelectItem key={m.id} value={m.id}>
                        {m.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* File Input */}
          <div className="w-full max-w-md">
            <input
              type="file"
              accept=".pdf"
              onChange={handleFileChange}
              className="hidden"
              id="file-upload"
            />
            <label
              htmlFor="file-upload"
              className="cursor-pointer block w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-all"
            >
              {files ? (
                <div>
                  <span className="text-blue-600">{files[0].name}</span>
                </div>
              ) : (
                <div>
                  <div className="text-gray-600">
                    クリックしてファイルをアップロード
                  </div>
                  <div className="text-sm text-gray-500 mt-1">
                    PDFファイルのみ
                  </div>
                </div>
              )}
            </label>
          </div>

          <Button
            type="submit"
            disabled={!files || isLoading}
            className="w-full max-w-md"
          >
            {isLoading ? "解析中..." : "ファイルを解析"}
          </Button>
        </div>

        {error && (
          <div className="text-red-500 text-center max-w-md mx-auto">
            {error}
          </div>
        )}

        {analysis && (
          <div className="bg-gray-50 p-6 rounded-lg space-y-4 mt-8">
            <div>
              <h3 className="font-bold text-lg">ジャンル</h3>
              <p className="text-gray-700">{analysis.genre}</p>
            </div>
            <div>
              <h3 className="font-bold text-lg">台本</h3>
              <div className="space-y-3">
                {analysis.scripts.map((script, index) => (
                  <div key={index} className="border-l-4 border-gray-200 pl-4">
                    <p className="font-medium text-gray-900">
                      {script.speaker}
                    </p>
                    <p className="text-gray-700">{script.lines}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  );
}

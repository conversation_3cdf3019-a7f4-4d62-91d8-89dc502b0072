"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";

interface ImageViewerProps {
  images: string[];
  currentPage: number;
  onPageChange: (page: number) => void;
}

export default function ImageViewer({
  images,
  currentPage,
  onPageChange,
}: ImageViewerProps) {
  if (!images.length) return null;

  const handlePrevious = () => {
    if (currentPage > 0) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < images.length - 1) {
      onPageChange(currentPage + 1);
    }
  };

  return (
    <div className="bg-gray-50 p-6 rounded-lg space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-bold text-lg">PDF プレビュー</h3>
        <div className="text-sm text-gray-600">
          {currentPage + 1} / {images.length} ページ
        </div>
      </div>

      <div className="relative">
        <Image
          src={images[currentPage]}
          alt={`PDF Page ${currentPage + 1}`}
          width={500}
          height={500}
          className="w-full max-w-md mx-auto border border-gray-200 rounded shadow-sm"
        />
      </div>

      <div className="flex justify-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleNext}
          disabled={currentPage === images.length - 1}
          className="flex items-center gap-1"
        >
          <ChevronLeft className="w-4 h-4" />
          次のページ
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={handlePrevious}
          disabled={currentPage === 0}
          className="flex items-center gap-1"
        >
          前のページ
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}

"use client";

interface FileUploaderProps {
  file: File | null;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export default function FileUploader({
  file,
  handleFileChange,
}: FileUploaderProps) {
  return (
    <div className="w-full max-w-md">
      <input
        type="file"
        accept=".pdf"
        onChange={handleFileChange}
        className="hidden"
        id="file-upload"
      />
      <label
        htmlFor="file-upload"
        className="cursor-pointer block w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-all"
      >
        {file ? (
          <div>
            <span className="text-blue-600">{file.name}</span>
          </div>
        ) : (
          <div>
            <div className="text-gray-600">
              クリックしてファイルをアップロード
            </div>
            <div className="text-sm text-gray-500 mt-1">PDFファイルのみ</div>
          </div>
        )}
      </label>
    </div>
  );
}

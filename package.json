{"name": "script-recognition", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@anthropic-ai/sdk": "^0.55.1", "@google/genai": "^1.8.0", "@pdfme/converter": "^5.4.0", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "canvas": "^3.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "docx": "^9.5.1", "lucide-react": "^0.525.0", "multer": "^2.0.1", "next": "15.3.4", "openai": "^5.8.2", "path2d-polyfill": "^3.2.1", "pdfjs-lib": "^0.0.149", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/multer": "^2.0.0", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}